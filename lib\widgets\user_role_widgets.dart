import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/user.dart';
import '../models/product.dart';
import '../services/auth_service.dart';
import '../controllers/cart_controller.dart';
import '../services/affiliate_service.dart';

// ويدجت شريط التطبيق حسب نوع المستخدم
class UserRoleAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;

  const UserRoleAppBar({super.key, required this.title});

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: Text(title),
      backgroundColor: Colors.teal,
      foregroundColor: Colors.white,
      actions: [
        Obx(() {
          final authService = Get.find<AuthService>();
          final user = authService.currentUser;

          if (user == null) {
            // للضيوف - زر تسجيل الدخول
            return _buildGuestActions();
          }

          switch (user.role) {
            case UserRole.customer:
              return _buildCustomerActions(user);
            case UserRole.affiliate:
              return _buildAffiliateActions(user);
            case UserRole.admin:
              return _buildAdminActions(user);
          }
        }),
      ],
    );
  }

  Widget _buildGuestActions() {
    return Row(
      children: [
        // زر الإعدادات
        IconButton(
          onPressed: () => Get.toNamed('/settings'),
          icon: const Icon(Icons.settings),
          tooltip: 'الإعدادات',
        ),

        TextButton.icon(
          onPressed: () => Get.toNamed('/login'),
          icon: const Icon(Icons.login, color: Colors.white),
          label: const Text('دخول', style: TextStyle(color: Colors.white)),
        ),
        const SizedBox(width: 8),
      ],
    );
  }

  Widget _buildCustomerActions(User user) {
    final cartController = Get.put(CartController());

    return Row(
      children: [
        // أيقونة سلة التسوق
        Stack(
          children: [
            IconButton(
              onPressed: () => Get.toNamed('/cart'),
              icon: const Icon(Icons.shopping_cart),
            ),
            Obx(() {
              final itemCount = cartController.itemCount.value;
              if (itemCount > 0) {
                return Positioned(
                  right: 6,
                  top: 6,
                  child: Container(
                    padding: const EdgeInsets.all(2),
                    decoration: BoxDecoration(
                      color: Colors.red,
                      borderRadius: BorderRadius.circular(10),
                    ),
                    constraints: const BoxConstraints(
                      minWidth: 16,
                      minHeight: 16,
                    ),
                    child: Text(
                      '$itemCount',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                );
              }
              return const SizedBox();
            }),
          ],
        ),

        // قائمة المستخدم
        PopupMenuButton<String>(
          icon: CircleAvatar(
            backgroundColor: Colors.white,
            child: Text(
              user.firstName[0].toUpperCase(),
              style: const TextStyle(color: Colors.teal),
            ),
          ),
          onSelected: (value) {
            switch (value) {
              case 'become_affiliate':
                Get.toNamed('/affiliate-request');
                break;
              case 'settings':
                Get.toNamed('/settings');
                break;
              case 'profile':
                // الانتقال لصفحة الملف الشخصي
                break;
              case 'orders':
                // الانتقال لصفحة الطلبات
                break;
              case 'logout':
                Get.find<AuthService>().logout();
                break;
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'become_affiliate',
              child: Row(
                children: [
                  Icon(Icons.trending_up, color: Colors.orange),
                  SizedBox(width: 8),
                  Text('أصبح مسوق'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'settings',
              child: Row(
                children: [
                  Icon(Icons.settings, color: Colors.grey),
                  SizedBox(width: 8),
                  Text('الإعدادات'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'profile',
              child: Row(
                children: [
                  Icon(Icons.person, color: Colors.blue),
                  SizedBox(width: 8),
                  Text('الملف الشخصي'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'orders',
              child: Row(
                children: [
                  Icon(Icons.shopping_bag, color: Colors.green),
                  SizedBox(width: 8),
                  Text('طلباتي'),
                ],
              ),
            ),
            const PopupMenuDivider(),
            const PopupMenuItem(
              value: 'logout',
              child: Row(
                children: [
                  Icon(Icons.logout, color: Colors.red),
                  SizedBox(width: 8),
                  Text('تسجيل الخروج'),
                ],
              ),
            ),
          ],
        ),
        const SizedBox(width: 8),
      ],
    );
  }

  Widget _buildAffiliateActions(User user) {
    return Row(
      children: [
        // قائمة المسوق
        PopupMenuButton<String>(
          icon: CircleAvatar(
            backgroundColor: Colors.orange,
            child: Text(
              user.firstName[0].toUpperCase(),
              style: const TextStyle(color: Colors.white),
            ),
          ),
          onSelected: (value) {
            switch (value) {
              case 'dashboard':
                Get.toNamed('/affiliate-dashboard');
                break;
              case 'create_link':
                _showCreateLinkDialog();
                break;
              case 'earnings':
                Get.toNamed('/affiliate-dashboard');
                break;
              case 'logout':
                Get.find<AuthService>().logout();
                break;
            }
          },
          itemBuilder: (context) => [
            PopupMenuItem(
              value: 'dashboard',
              child: Row(
                children: [
                  const Icon(Icons.dashboard, color: Colors.blue),
                  const SizedBox(width: 8),
                  const Text('لوحة التحكم'),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'create_link',
              child: Row(
                children: [
                  const Icon(Icons.link, color: Colors.green),
                  const SizedBox(width: 8),
                  const Text('إنشاء رابط'),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'earnings',
              child: Row(
                children: [
                  const Icon(Icons.monetization_on, color: Colors.orange),
                  const SizedBox(width: 8),
                  Text('الأرباح: \$${user.totalEarnings.toStringAsFixed(2)}'),
                ],
              ),
            ),
            const PopupMenuDivider(),
            const PopupMenuItem(
              value: 'logout',
              child: Row(
                children: [
                  Icon(Icons.logout, color: Colors.red),
                  SizedBox(width: 8),
                  Text('تسجيل الخروج'),
                ],
              ),
            ),
          ],
        ),
        const SizedBox(width: 8),
      ],
    );
  }

  Widget _buildAdminActions(User user) {
    return Row(
      children: [
        // زر لوحة الإدارة
        IconButton(
          onPressed: () => Get.toNamed('/admin-dashboard'),
          icon: const Icon(Icons.admin_panel_settings),
          tooltip: 'لوحة الإدارة',
        ),

        // قائمة الأدمن
        PopupMenuButton<String>(
          icon: CircleAvatar(
            backgroundColor: Colors.red,
            child: Text(
              user.firstName[0].toUpperCase(),
              style: const TextStyle(color: Colors.white),
            ),
          ),
          onSelected: (value) {
            switch (value) {
              case 'admin_dashboard':
                Get.toNamed('/admin-dashboard');
                break;
              case 'users':
                // إدارة المستخدمين
                break;
              case 'products':
                // إدارة المنتجات
                break;
              case 'orders':
                // إدارة الطلبات
                break;
              case 'logout':
                Get.find<AuthService>().logout();
                break;
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'admin_dashboard',
              child: Row(
                children: [
                  Icon(Icons.dashboard, color: Colors.blue),
                  SizedBox(width: 8),
                  Text('لوحة الإدارة'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'users',
              child: Row(
                children: [
                  Icon(Icons.people, color: Colors.green),
                  SizedBox(width: 8),
                  Text('إدارة المستخدمين'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'products',
              child: Row(
                children: [
                  Icon(Icons.inventory, color: Colors.orange),
                  SizedBox(width: 8),
                  Text('إدارة المنتجات'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'orders',
              child: Row(
                children: [
                  Icon(Icons.shopping_bag, color: Colors.purple),
                  SizedBox(width: 8),
                  Text('إدارة الطلبات'),
                ],
              ),
            ),
            const PopupMenuDivider(),
            const PopupMenuItem(
              value: 'logout',
              child: Row(
                children: [
                  Icon(Icons.logout, color: Colors.red),
                  SizedBox(width: 8),
                  Text('تسجيل الخروج'),
                ],
              ),
            ),
          ],
        ),
        const SizedBox(width: 8),
      ],
    );
  }

  void _showBecomeAffiliateDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('أصبح مسوق'),
        content: const Text(
          'هل تريد التسجيل كمسوق والحصول على عمولة من كل عملية بيع؟\n\n'
          '• احصل على عمولة من كل عملية بيع\n'
          '• أنشئ روابط تسويقية مخصصة\n'
          '• تتبع أرباحك وإحصائياتك',
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              // تحويل المستخدم لمسوق
              _convertToAffiliate();
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.orange),
            child: const Text('أصبح مسوق'),
          ),
        ],
      ),
    );
  }

  void _showCreateLinkDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('إنشاء رابط تسويقي'),
        content: const Text('سيتم إنشاء رابط تسويقي للصفحة الرئيسية'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              _createGeneralLink();
            },
            child: const Text('إنشاء'),
          ),
        ],
      ),
    );
  }

  void _convertToAffiliate() async {
    final authService = Get.find<AuthService>();
    final currentUser = authService.currentUser;

    if (currentUser != null && currentUser.isCustomer) {
      // تحويل المستخدم لمسوق
      final success = await authService.convertToAffiliate(currentUser.id);

      if (success) {
        Get.snackbar(
          'تم التسجيل',
          'تم تسجيلك كمسوق بنجاح! يمكنك الآن إنشاء روابط تسويقية من الإعدادات',
          snackPosition: SnackPosition.TOP,
          duration: const Duration(seconds: 4),
        );

        // البقاء في الصفحة الحالية - لا حاجة للتوجيه
      }
    }
  }

  void _createGeneralLink() async {
    final authService = Get.find<AuthService>();
    final user = authService.currentUser;

    if (user != null && user.isAffiliate) {
      final affiliateService = Get.find<AffiliateService>();
      // إنشاء رابط عام للصفحة الرئيسية
      Get.snackbar(
        'تم الإنشاء',
        'تم إنشاء رابط تسويقي جديد',
        snackPosition: SnackPosition.TOP,
      );
    }
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

// ويدجت أزرار المنتج حسب نوع المستخدم
class ProductActionButtons extends StatelessWidget {
  final Product product;

  const ProductActionButtons({super.key, required this.product});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final authService = Get.find<AuthService>();
      final user = authService.currentUser;

      if (user == null) {
        return _buildGuestButtons();
      }

      switch (user.role) {
        case UserRole.customer:
          return _buildCustomerButtons();
        case UserRole.affiliate:
          return _buildAffiliateButtons();
        case UserRole.admin:
          return _buildAdminButtons();
      }
    });
  }

  Widget _buildGuestButtons() {
    return ElevatedButton(
      onPressed: () => Get.toNamed('/login'),
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.teal,
        foregroundColor: Colors.white,
      ),
      child: const Text('سجل دخولك للشراء'),
    );
  }

  Widget _buildCustomerButtons() {
    final cartController = Get.put(CartController());

    return Row(
      children: [
        Expanded(
          child: ElevatedButton.icon(
            onPressed: () => cartController.addToCart(product),
            icon: const Icon(Icons.add_shopping_cart),
            label: const Text('أضف للسلة'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.teal,
              foregroundColor: Colors.white,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildAffiliateButtons() {
    return Column(
      children: [
        // رسالة توضيحية للمسوقين
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.orange.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.orange.withOpacity(0.3)),
          ),
          child: const Row(
            children: [
              Icon(Icons.info, color: Colors.orange, size: 20),
              SizedBox(width: 8),
              Expanded(
                child: Text(
                  'كمسوق، يمكنك إنشاء روابط تسويقية وكسب العمولات',
                  style: TextStyle(
                    color: Colors.orange,
                    fontSize: 12,
                  ),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () => _createProductLink(),
                icon: const Icon(Icons.link),
                label: const Text('إنشاء رابط تسويقي'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildAdminButtons() {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton.icon(
            onPressed: () => _editProduct(),
            icon: const Icon(Icons.edit),
            label: const Text('تعديل'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
            ),
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: ElevatedButton.icon(
            onPressed: () => _createProductLink(),
            icon: const Icon(Icons.link),
            label: const Text('رابط'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
            ),
          ),
        ),
      ],
    );
  }

  void _createProductLink() {
    Get.snackbar(
      'تم الإنشاء',
      'تم إنشاء رابط تسويقي للمنتج: ${product.name}',
      snackPosition: SnackPosition.TOP,
    );
  }

  void _editProduct() {
    Get.snackbar(
      'تعديل المنتج',
      'سيتم فتح صفحة تعديل المنتج: ${product.name}',
      snackPosition: SnackPosition.TOP,
    );
  }
}
