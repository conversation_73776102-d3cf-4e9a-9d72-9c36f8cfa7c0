import 'package:get/get.dart';
import '../screens/home/<USER>';
import '../screens/auth/login_screen.dart';
import '../screens/auth/register_screen.dart';
import '../screens/auth/reset_password_screen.dart';
import '../screens/affiliate/affiliate_dashboard.dart';
import '../screens/affiliate/affiliate_request_screen.dart';
import '../screens/cart/cart_screen.dart';
import '../screens/admin/admin_dashboard.dart';
import '../screens/settings/settings_screen.dart';
import '../services/auth_service.dart';

class AppPages {
  static const initial = '/';

  static final routes = [
    // الصفحة الرئيسية
    GetPage(
      name: '/',
      page: () => const HomeScreen(),
    ),

    // شاشات المصادقة
    GetPage(
      name: '/login',
      page: () => const LoginScreen(),
    ),
    GetPage(
      name: '/register',
      page: () => const RegisterScreen(),
    ),
    GetPage(
      name: '/reset-password',
      page: () => const ResetPasswordScreen(),
    ),

    // شاشة سلة التسوق
    GetPage(
      name: '/cart',
      page: () => const CartScreen(),
    ),

    // شاشة الإعدادات
    GetPage(
      name: '/settings',
      page: () => const SettingsScreen(),
    ),

    // شاشات المسوق
    GetPage(
      name: '/affiliate-dashboard',
      page: () => const AffiliateDashboard(),
    ),

    // شاشة طلب التحول لمسوق
    GetPage(
      name: '/affiliate-request',
      page: () => const AffiliateRequestScreen(),
    ),

    // شاشات الإدمن
    GetPage(
      name: '/admin-dashboard',
      page: () => const AdminDashboard(),
    ),
  ];
}
