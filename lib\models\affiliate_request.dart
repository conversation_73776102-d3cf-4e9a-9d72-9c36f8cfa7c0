enum AffiliateRequestStatus { pending, approved, rejected }

class AffiliateRequest {
  final String id;
  final String userId;
  final String userFullName;
  final String userEmail;
  final String userPhone;
  final String reason; // سبب الرغبة في التسويق
  final String experience; // الخبرة في التسويق
  final String socialMediaLinks; // روابط وسائل التواصل
  final AffiliateRequestStatus status;
  final DateTime requestDate;
  final DateTime? reviewDate;
  final String? reviewedBy; // معرف الأدمن الذي راجع الطلب
  final String? reviewNotes; // ملاحظات المراجعة

  AffiliateRequest({
    required this.id,
    required this.userId,
    required this.userFullName,
    required this.userEmail,
    required this.userPhone,
    required this.reason,
    required this.experience,
    required this.socialMediaLinks,
    this.status = AffiliateRequestStatus.pending,
    required this.requestDate,
    this.reviewDate,
    this.reviewedBy,
    this.reviewNotes,
  });

  bool get isPending => status == AffiliateRequestStatus.pending;
  bool get isApproved => status == AffiliateRequestStatus.approved;
  bool get isRejected => status == AffiliateRequestStatus.rejected;

  String get statusText {
    switch (status) {
      case AffiliateRequestStatus.pending:
        return 'قيد المراجعة';
      case AffiliateRequestStatus.approved:
        return 'مقبول';
      case AffiliateRequestStatus.rejected:
        return 'مرفوض';
    }
  }

  Color get statusColor {
    switch (status) {
      case AffiliateRequestStatus.pending:
        return Colors.orange;
      case AffiliateRequestStatus.approved:
        return Colors.green;
      case AffiliateRequestStatus.rejected:
        return Colors.red;
    }
  }

  AffiliateRequest copyWith({
    String? id,
    String? userId,
    String? userFullName,
    String? userEmail,
    String? userPhone,
    String? reason,
    String? experience,
    String? socialMediaLinks,
    AffiliateRequestStatus? status,
    DateTime? requestDate,
    DateTime? reviewDate,
    String? reviewedBy,
    String? reviewNotes,
  }) {
    return AffiliateRequest(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      userFullName: userFullName ?? this.userFullName,
      userEmail: userEmail ?? this.userEmail,
      userPhone: userPhone ?? this.userPhone,
      reason: reason ?? this.reason,
      experience: experience ?? this.experience,
      socialMediaLinks: socialMediaLinks ?? this.socialMediaLinks,
      status: status ?? this.status,
      requestDate: requestDate ?? this.requestDate,
      reviewDate: reviewDate ?? this.reviewDate,
      reviewedBy: reviewedBy ?? this.reviewedBy,
      reviewNotes: reviewNotes ?? this.reviewNotes,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'userFullName': userFullName,
      'userEmail': userEmail,
      'userPhone': userPhone,
      'reason': reason,
      'experience': experience,
      'socialMediaLinks': socialMediaLinks,
      'status': status.toString(),
      'requestDate': requestDate.toIso8601String(),
      'reviewDate': reviewDate?.toIso8601String(),
      'reviewedBy': reviewedBy,
      'reviewNotes': reviewNotes,
    };
  }

  factory AffiliateRequest.fromJson(Map<String, dynamic> json) {
    return AffiliateRequest(
      id: json['id'] ?? '',
      userId: json['userId'] ?? '',
      userFullName: json['userFullName'] ?? '',
      userEmail: json['userEmail'] ?? '',
      userPhone: json['userPhone'] ?? '',
      reason: json['reason'] ?? '',
      experience: json['experience'] ?? '',
      socialMediaLinks: json['socialMediaLinks'] ?? '',
      status: AffiliateRequestStatus.values.firstWhere(
        (e) => e.toString() == json['status'],
        orElse: () => AffiliateRequestStatus.pending,
      ),
      requestDate: DateTime.parse(json['requestDate'] ?? DateTime.now().toIso8601String()),
      reviewDate: json['reviewDate'] != null ? DateTime.parse(json['reviewDate']) : null,
      reviewedBy: json['reviewedBy'],
      reviewNotes: json['reviewNotes'],
    );
  }
}

// خدمة إدارة طلبات التحول للمسوقين
class AffiliateRequestService {
  static final List<AffiliateRequest> _requests = [];

  // إرسال طلب تحول لمسوق
  static Future<bool> submitRequest({
    required String userId,
    required String userFullName,
    required String userEmail,
    required String userPhone,
    required String reason,
    required String experience,
    required String socialMediaLinks,
  }) async {
    try {
      // التحقق من عدم وجود طلب سابق
      final existingRequest = _requests.where((r) => r.userId == userId && r.isPending).firstOrNull;
      if (existingRequest != null) {
        return false; // يوجد طلب قيد المراجعة
      }

      final request = AffiliateRequest(
        id: 'req_${DateTime.now().millisecondsSinceEpoch}',
        userId: userId,
        userFullName: userFullName,
        userEmail: userEmail,
        userPhone: userPhone,
        reason: reason,
        experience: experience,
        socialMediaLinks: socialMediaLinks,
        requestDate: DateTime.now(),
      );

      _requests.add(request);
      return true;
    } catch (e) {
      return false;
    }
  }

  // الحصول على طلبات المراجعة (للأدمن)
  static List<AffiliateRequest> getPendingRequests() {
    return _requests.where((r) => r.isPending).toList();
  }

  // الحصول على جميع الطلبات (للأدمن)
  static List<AffiliateRequest> getAllRequests() {
    return List.from(_requests);
  }

  // الحصول على طلب المستخدم
  static AffiliateRequest? getUserRequest(String userId) {
    try {
      return _requests.where((r) => r.userId == userId).lastOrNull;
    } catch (e) {
      return null;
    }
  }

  // مراجعة طلب (للأدمن)
  static Future<bool> reviewRequest({
    required String requestId,
    required AffiliateRequestStatus status,
    required String reviewedBy,
    String? reviewNotes,
  }) async {
    try {
      final index = _requests.indexWhere((r) => r.id == requestId);
      if (index == -1) return false;

      _requests[index] = _requests[index].copyWith(
        status: status,
        reviewDate: DateTime.now(),
        reviewedBy: reviewedBy,
        reviewNotes: reviewNotes,
      );

      return true;
    } catch (e) {
      return false;
    }
  }

  // إحصائيات الطلبات
  static Map<String, int> getRequestsStats() {
    return {
      'total': _requests.length,
      'pending': _requests.where((r) => r.isPending).length,
      'approved': _requests.where((r) => r.isApproved).length,
      'rejected': _requests.where((r) => r.isRejected).length,
    };
  }
}
