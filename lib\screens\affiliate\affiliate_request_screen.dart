import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../models/affiliate_request.dart';
import '../../services/auth_service.dart';
import '../../widgets/user_role_widgets.dart';

class AffiliateRequestScreen extends StatefulWidget {
  const AffiliateRequestScreen({super.key});

  @override
  State<AffiliateRequestScreen> createState() => _AffiliateRequestScreenState();
}

class _AffiliateRequestScreenState extends State<AffiliateRequestScreen> {
  final _formKey = GlobalKey<FormState>();
  final _reasonController = TextEditingController();
  final _experienceController = TextEditingController();
  final _socialMediaController = TextEditingController();
  
  bool _isLoading = false;

  @override
  void dispose() {
    _reasonController.dispose();
    _experienceController.dispose();
    _socialMediaController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final user = Get.find<AuthService>().currentUser;
    
    if (user == null) {
      return Scaffold(
        appBar: AppBar(title: const Text('خطأ')),
        body: const Center(child: Text('يجب تسجيل الدخول أولاً')),
      );
    }

    // التحقق من وجود طلب سابق
    final existingRequest = AffiliateRequestService.getUserRequest(user.id);

    return Scaffold(
      appBar: const UserRoleAppBar(title: 'طلب أن تصبح مسوق'),
      body: existingRequest != null 
          ? _buildExistingRequestView(existingRequest)
          : _buildRequestForm(user),
    );
  }

  Widget _buildExistingRequestView(AffiliateRequest request) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        request.isPending ? Icons.hourglass_empty :
                        request.isApproved ? Icons.check_circle :
                        Icons.cancel,
                        color: request.statusColor,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'حالة الطلب: ${request.statusText}',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: request.statusColor,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Text('تاريخ الطلب: ${_formatDate(request.requestDate)}'),
                  if (request.reviewDate != null)
                    Text('تاريخ المراجعة: ${_formatDate(request.reviewDate!)}'),
                  if (request.reviewNotes != null && request.reviewNotes!.isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.only(top: 8),
                      child: Text(
                        'ملاحظات: ${request.reviewNotes}',
                        style: const TextStyle(fontStyle: FontStyle.italic),
                      ),
                    ),
                ],
              ),
            ),
          ),
          
          if (request.isPending) ...[
            const SizedBox(height: 16),
            const Card(
              color: Colors.orange,
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Row(
                  children: [
                    Icon(Icons.info, color: Colors.white),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'طلبك قيد المراجعة. سيتم إشعارك بالنتيجة قريباً.',
                        style: TextStyle(color: Colors.white),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
          
          if (request.isApproved) ...[
            const SizedBox(height: 16),
            Card(
              color: Colors.green,
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    const Row(
                      children: [
                        Icon(Icons.celebration, color: Colors.white),
                        SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'تهانينا! تم قبول طلبك كمسوق',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: () => Get.offAllNamed('/affiliate-dashboard'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.white,
                          foregroundColor: Colors.green,
                        ),
                        child: const Text('انتقل للوحة التحكم'),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
          
          if (request.isRejected) ...[
            const SizedBox(height: 16),
            Card(
              color: Colors.red,
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    const Row(
                      children: [
                        Icon(Icons.error, color: Colors.white),
                        SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'تم رفض طلبك',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: () => _showNewRequestDialog(),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.white,
                          foregroundColor: Colors.red,
                        ),
                        child: const Text('تقديم طلب جديد'),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildRequestForm(user) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Card(
              color: Colors.blue,
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Row(
                  children: [
                    Icon(Icons.info, color: Colors.white),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'املأ النموذج أدناه لتقديم طلب أن تصبح مسوق معتمد',
                        style: TextStyle(color: Colors.white),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 24),
            
            TextFormField(
              controller: _reasonController,
              decoration: const InputDecoration(
                labelText: 'لماذا تريد أن تصبح مسوق؟ *',
                hintText: 'اشرح أسباب رغبتك في التسويق...',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'هذا الحقل مطلوب';
                }
                if (value.trim().length < 20) {
                  return 'يجب أن يكون النص 20 حرف على الأقل';
                }
                return null;
              },
            ),
            
            const SizedBox(height: 16),
            
            TextFormField(
              controller: _experienceController,
              decoration: const InputDecoration(
                labelText: 'خبرتك في التسويق *',
                hintText: 'اذكر خبرتك السابقة في التسويق أو المبيعات...',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'هذا الحقل مطلوب';
                }
                return null;
              },
            ),
            
            const SizedBox(height: 16),
            
            TextFormField(
              controller: _socialMediaController,
              decoration: const InputDecoration(
                labelText: 'روابط وسائل التواصل الاجتماعي',
                hintText: 'Facebook, Instagram, TikTok, etc...',
                border: OutlineInputBorder(),
              ),
              maxLines: 2,
            ),
            
            const SizedBox(height: 24),
            
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isLoading ? null : _submitRequest,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: _isLoading
                    ? const CircularProgressIndicator(color: Colors.white)
                    : const Text(
                        'إرسال الطلب',
                        style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                      ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _submitRequest() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    final user = Get.find<AuthService>().currentUser!;
    
    final success = await AffiliateRequestService.submitRequest(
      userId: user.id,
      userFullName: user.fullName,
      userEmail: user.email,
      userPhone: user.phoneNumber ?? '',
      reason: _reasonController.text.trim(),
      experience: _experienceController.text.trim(),
      socialMediaLinks: _socialMediaController.text.trim(),
    );

    setState(() => _isLoading = false);

    if (success) {
      Get.snackbar(
        'تم الإرسال',
        'تم إرسال طلبك بنجاح. سيتم مراجعته والرد عليك قريباً.',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
      setState(() {}); // لإعادة بناء الواجهة
    } else {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أو يوجد طلب سابق قيد المراجعة',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  void _showNewRequestDialog() {
    // يمكن إضافة منطق لتقديم طلب جديد بعد الرفض
    Get.snackbar('طلب جديد', 'يمكنك تقديم طلب جديد بعد 30 يوم من الرفض');
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
