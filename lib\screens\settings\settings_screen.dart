import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../models/user.dart';
import '../../models/affiliate_request.dart';
import '../../services/auth_service.dart';
import '../../widgets/user_role_widgets.dart';

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const UserRoleAppBar(title: 'الإعدادات'),
      body: Obx(() {
        final authService = Get.find<AuthService>();
        final user = authService.currentUser;

        if (user == null) {
          return _buildGuestSettings();
        }

        switch (user.role) {
          case UserRole.customer:
            return _buildCustomerSettings(user);
          case UserRole.affiliate:
            return _buildAffiliateSettings(user);
          case UserRole.admin:
            return _buildAdminSettings(user);
        }
      }),
    );
  }

  // إعدادات الضيوف
  Widget _buildGuestSettings() {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        const Card(
          color: Colors.blue,
          child: Padding(
            padding: EdgeInsets.all(16),
            child: Row(
              children: [
                Icon(Icons.info, color: Colors.white),
                SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'سجل دخولك للوصول لجميع الميزات',
                    style: TextStyle(color: Colors.white),
                  ),
                ),
              ],
            ),
          ),
        ),
        
        const SizedBox(height: 16),
        
        _buildSettingsSection('الحساب', [
          _buildSettingsTile(
            'تسجيل الدخول',
            'ادخل لحسابك الموجود',
            Icons.login,
            Colors.green,
            () => Get.toNamed('/login'),
          ),
          _buildSettingsTile(
            'إنشاء حساب جديد',
            'انشئ حساب جديد مجاناً',
            Icons.person_add,
            Colors.blue,
            () => Get.toNamed('/register'),
          ),
        ]),
        
        _buildSettingsSection('عام', [
          _buildSettingsTile(
            'اللغة',
            'العربية',
            Icons.language,
            Colors.orange,
            () => _showLanguageDialog(),
          ),
          _buildSettingsTile(
            'العملة',
            'الدينار الجزائري (دج)',
            Icons.monetization_on,
            Colors.green,
            () => _showCurrencyDialog(),
          ),
        ]),
        
        _buildSettingsSection('المساعدة', [
          _buildSettingsTile(
            'اتصل بنا',
            '+************',
            Icons.phone,
            Colors.blue,
            () => _contactUs(),
          ),
          _buildSettingsTile(
            'الأسئلة الشائعة',
            'احصل على إجابات سريعة',
            Icons.help,
            Colors.purple,
            () => _showFAQ(),
          ),
        ]),
      ],
    );
  }

  // إعدادات العملاء
  Widget _buildCustomerSettings(User user) {
    final affiliateRequest = AffiliateRequestService.getUserRequest(user.id);
    
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildUserProfile(user),
        
        const SizedBox(height: 16),
        
        _buildSettingsSection('الحساب', [
          _buildSettingsTile(
            'الملف الشخصي',
            'تعديل بياناتك الشخصية',
            Icons.person,
            Colors.blue,
            () => _editProfile(),
          ),
          _buildSettingsTile(
            'طلباتي',
            'تتبع طلباتك وتاريخ الشراء',
            Icons.shopping_bag,
            Colors.green,
            () => _viewOrders(),
          ),
          _buildSettingsTile(
            'العناوين',
            'إدارة عناوين التوصيل',
            Icons.location_on,
            Colors.red,
            () => _manageAddresses(),
          ),
        ]),
        
        _buildSettingsSection('التسويق', [
          _buildAffiliateRequestTile(affiliateRequest),
        ]),
        
        _buildSettingsSection('عام', [
          _buildSettingsTile(
            'الإشعارات',
            'إدارة إشعارات التطبيق',
            Icons.notifications,
            Colors.orange,
            () => _manageNotifications(),
          ),
        ]),
        
        _buildSettingsSection('المساعدة', [
          _buildSettingsTile(
            'اتصل بنا',
            '+************',
            Icons.phone,
            Colors.blue,
            () => _contactUs(),
          ),
          _buildSettingsTile(
            'تسجيل الخروج',
            'خروج من الحساب',
            Icons.logout,
            Colors.red,
            () => _logout(),
          ),
        ]),
      ],
    );
  }

  // إعدادات المسوقين
  Widget _buildAffiliateSettings(User user) {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildUserProfile(user),
        
        const SizedBox(height: 16),
        
        _buildSettingsSection('لوحة التحكم', [
          _buildSettingsTile(
            'لوحة تحكم المسوق',
            'إحصائيات وأرباح',
            Icons.dashboard,
            Colors.orange,
            () => Get.toNamed('/affiliate-dashboard'),
          ),
        ]),
        
        _buildSettingsSection('التسويق', [
          _buildSettingsTile(
            'الروابط التسويقية',
            'إدارة روابطك التسويقية',
            Icons.link,
            Colors.blue,
            () => _manageAffiliateLinks(),
          ),
          _buildSettingsTile(
            'العمولات',
            'تتبع أرباحك وعمولاتك',
            Icons.monetization_on,
            Colors.green,
            () => _viewCommissions(),
          ),
          _buildSettingsTile(
            'طلب صرف',
            'اطلب صرف أرباحك',
            Icons.payment,
            Colors.purple,
            () => _requestPayout(),
          ),
        ]),
        
        _buildSettingsSection('الحساب', [
          _buildSettingsTile(
            'الملف الشخصي',
            'تعديل بياناتك الشخصية',
            Icons.person,
            Colors.blue,
            () => _editProfile(),
          ),
        ]),
        
        _buildSettingsSection('المساعدة', [
          _buildSettingsTile(
            'دليل المسوق',
            'تعلم كيفية التسويق بفعالية',
            Icons.school,
            Colors.indigo,
            () => _showAffiliateGuide(),
          ),
          _buildSettingsTile(
            'اتصل بنا',
            '+************',
            Icons.phone,
            Colors.blue,
            () => _contactUs(),
          ),
          _buildSettingsTile(
            'تسجيل الخروج',
            'خروج من الحساب',
            Icons.logout,
            Colors.red,
            () => _logout(),
          ),
        ]),
      ],
    );
  }

  // إعدادات الأدمن
  Widget _buildAdminSettings(User user) {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildUserProfile(user),
        
        const SizedBox(height: 16),
        
        _buildSettingsSection('الإدارة', [
          _buildSettingsTile(
            'لوحة الإدارة',
            'إدارة شاملة للنظام',
            Icons.admin_panel_settings,
            Colors.red,
            () => Get.toNamed('/admin-dashboard'),
          ),
          _buildSettingsTile(
            'طلبات المسوقين',
            'مراجعة طلبات التحول للمسوقين',
            Icons.pending_actions,
            Colors.orange,
            () => _manageAffiliateRequests(),
          ),
        ]),
        
        _buildSettingsSection('إدارة المحتوى', [
          _buildSettingsTile(
            'إدارة المنتجات',
            'إضافة وتعديل المنتجات',
            Icons.inventory,
            Colors.green,
            () => _manageProducts(),
          ),
          _buildSettingsTile(
            'إدارة المستخدمين',
            'إدارة حسابات المستخدمين',
            Icons.people,
            Colors.blue,
            () => _manageUsers(),
          ),
          _buildSettingsTile(
            'إدارة الطلبات',
            'متابعة ومعالجة الطلبات',
            Icons.shopping_cart,
            Colors.purple,
            () => _manageOrders(),
          ),
        ]),
        
        _buildSettingsSection('النظام', [
          _buildSettingsTile(
            'إعدادات التطبيق',
            'إعدادات عامة للنظام',
            Icons.settings,
            Colors.grey,
            () => _systemSettings(),
          ),
          _buildSettingsTile(
            'التقارير',
            'تقارير مفصلة وإحصائيات',
            Icons.analytics,
            Colors.indigo,
            () => _viewReports(),
          ),
        ]),
        
        _buildSettingsSection('الحساب', [
          _buildSettingsTile(
            'تسجيل الخروج',
            'خروج من الحساب',
            Icons.logout,
            Colors.red,
            () => _logout(),
          ),
        ]),
      ],
    );
  }

  Widget _buildUserProfile(User user) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            CircleAvatar(
              radius: 30,
              backgroundColor: _getUserRoleColor(user.role),
              child: Text(
                user.firstName[0].toUpperCase(),
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    user.fullName,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    user.email,
                    style: const TextStyle(color: Colors.grey),
                  ),
                  const SizedBox(height: 4),
                  Chip(
                    label: Text(_getUserRoleText(user.role)),
                    backgroundColor: _getUserRoleColor(user.role).withOpacity(0.1),
                    labelStyle: TextStyle(color: _getUserRoleColor(user.role)),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSettingsSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: Text(
            title,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.grey,
            ),
          ),
        ),
        Card(
          child: Column(children: children),
        ),
        const SizedBox(height: 16),
      ],
    );
  }

  Widget _buildSettingsTile(
    String title,
    String subtitle,
    IconData icon,
    Color iconColor,
    VoidCallback onTap,
  ) {
    return ListTile(
      leading: Icon(icon, color: iconColor),
      title: Text(title),
      subtitle: Text(subtitle),
      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
      onTap: onTap,
    );
  }

  Widget _buildAffiliateRequestTile(AffiliateRequest? request) {
    if (request == null) {
      return _buildSettingsTile(
        'أصبح مسوق',
        'اطلب أن تصبح مسوق معتمد',
        Icons.trending_up,
        Colors.orange,
        () => Get.toNamed('/affiliate-request'),
      );
    }

    return _buildSettingsTile(
      'طلب التسويق',
      'الحالة: ${request.statusText}',
      request.isPending ? Icons.hourglass_empty :
      request.isApproved ? Icons.check_circle : Icons.cancel,
      request.statusColor,
      () => Get.toNamed('/affiliate-request'),
    );
  }

  Color _getUserRoleColor(UserRole role) {
    switch (role) {
      case UserRole.customer:
        return Colors.green;
      case UserRole.affiliate:
        return Colors.orange;
      case UserRole.admin:
        return Colors.red;
    }
  }

  String _getUserRoleText(UserRole role) {
    switch (role) {
      case UserRole.customer:
        return 'عميل';
      case UserRole.affiliate:
        return 'مسوق';
      case UserRole.admin:
        return 'مدير';
    }
  }

  // دوال الإجراءات
  void _showLanguageDialog() => Get.snackbar('اللغة', 'العربية هي اللغة الوحيدة المتاحة حالياً');
  void _showCurrencyDialog() => Get.snackbar('العملة', 'الدينار الجزائري هو العملة الوحيدة المتاحة');
  void _contactUs() => Get.snackbar('اتصل بنا', 'الهاتف: +************\nالبريد: <EMAIL>');
  void _showFAQ() => Get.snackbar('الأسئلة الشائعة', 'سيتم فتح صفحة الأسئلة الشائعة');
  void _editProfile() => Get.snackbar('الملف الشخصي', 'سيتم فتح صفحة تعديل الملف الشخصي');
  void _viewOrders() => Get.snackbar('طلباتي', 'سيتم فتح صفحة الطلبات');
  void _manageAddresses() => Get.snackbar('العناوين', 'سيتم فتح صفحة إدارة العناوين');
  void _manageNotifications() => Get.snackbar('الإشعارات', 'سيتم فتح إعدادات الإشعارات');
  void _manageAffiliateLinks() => Get.toNamed('/affiliate-dashboard');
  void _viewCommissions() => Get.toNamed('/affiliate-dashboard');
  void _requestPayout() => Get.snackbar('طلب صرف', 'سيتم فتح صفحة طلب الصرف');
  void _showAffiliateGuide() => Get.snackbar('دليل المسوق', 'سيتم فتح دليل التسويق');
  void _manageAffiliateRequests() => Get.snackbar('طلبات المسوقين', 'سيتم فتح صفحة إدارة طلبات المسوقين');
  void _manageProducts() => Get.snackbar('إدارة المنتجات', 'سيتم فتح صفحة إدارة المنتجات');
  void _manageUsers() => Get.snackbar('إدارة المستخدمين', 'سيتم فتح صفحة إدارة المستخدمين');
  void _manageOrders() => Get.snackbar('إدارة الطلبات', 'سيتم فتح صفحة إدارة الطلبات');
  void _systemSettings() => Get.snackbar('إعدادات النظام', 'سيتم فتح إعدادات النظام');
  void _viewReports() => Get.snackbar('التقارير', 'سيتم فتح صفحة التقارير');
  
  void _logout() {
    Get.dialog(
      AlertDialog(
        title: const Text('تسجيل الخروج'),
        content: const Text('هل أنت متأكد من تسجيل الخروج؟'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              Get.find<AuthService>().logout();
              Get.offAllNamed('/');
            },
            child: const Text('تأكيد'),
          ),
        ],
      ),
    );
  }
}
