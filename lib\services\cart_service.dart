import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../models/cart.dart';
import '../models/product.dart';

class CartService extends GetxService {
  static CartService get instance => Get.find<CartService>();

  final Rx<Cart?> _cart = Rx<Cart?>(null);
  
  Cart? get cart => _cart.value;
  RxInt get itemCount => (cart?.totalItems ?? 0).obs;
  RxDouble get totalPrice => (cart?.totalPrice ?? 0.0).obs;

  @override
  Future<void> onInit() async {
    super.onInit();
    await _loadCartFromStorage();
  }

  // تحميل السلة من التخزين المحلي
  Future<void> _loadCartFromStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cartJson = prefs.getString('user_cart');
      
      if (cartJson != null) {
        final cartData = json.decode(cartJson);
        _cart.value = Cart.fromJson(cartData);
      }
    } catch (e) {
      print('خطأ في تحميل السلة: $e');
    }
  }

  // حفظ السلة في التخزين المحلي
  Future<void> _saveCartToStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      if (_cart.value != null) {
        final cartJson = json.encode(_cart.value!.toJson());
        await prefs.setString('user_cart', cartJson);
      } else {
        await prefs.remove('user_cart');
      }
    } catch (e) {
      print('خطأ في حفظ السلة: $e');
    }
  }

  // إنشاء سلة جديدة للمستخدم
  Future<void> createCart(String userId) async {
    _cart.value = Cart.empty(userId);
    await _saveCartToStorage();
  }

  // إضافة منتج للسلة
  Future<bool> addToCart(Product product, {int quantity = 1}) async {
    try {
      if (_cart.value == null) {
        return false;
      }

      // التحقق من توفر المنتج
      if (product.stockQuantity < quantity) {
        Get.snackbar(
          'غير متوفر',
          'الكمية المطلوبة غير متوفرة في المخزون',
          snackPosition: SnackPosition.TOP,
        );
        return false;
      }

      _cart.value = _cart.value!.addItem(product, quantity: quantity);
      await _saveCartToStorage();

      Get.snackbar(
        'تمت الإضافة',
        'تم إضافة ${product.name} إلى السلة',
        snackPosition: SnackPosition.TOP,
      );

      return true;
    } catch (e) {
      print('خطأ في إضافة المنتج للسلة: $e');
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء إضافة المنتج للسلة',
        snackPosition: SnackPosition.TOP,
      );
      return false;
    }
  }

  // تحديث كمية منتج في السلة
  Future<bool> updateQuantity(int productId, int quantity) async {
    try {
      if (_cart.value == null) return false;

      _cart.value = _cart.value!.updateItemQuantity(productId, quantity);
      await _saveCartToStorage();

      return true;
    } catch (e) {
      print('خطأ في تحديث الكمية: $e');
      return false;
    }
  }

  // حذف منتج من السلة
  Future<bool> removeFromCart(int productId) async {
    try {
      if (_cart.value == null) return false;

      _cart.value = _cart.value!.removeItem(productId);
      await _saveCartToStorage();

      Get.snackbar(
        'تم الحذف',
        'تم حذف المنتج من السلة',
        snackPosition: SnackPosition.TOP,
      );

      return true;
    } catch (e) {
      print('خطأ في حذف المنتج: $e');
      return false;
    }
  }

  // تفريغ السلة
  Future<void> clearCart() async {
    try {
      if (_cart.value == null) return;

      _cart.value = _cart.value!.clear();
      await _saveCartToStorage();

      Get.snackbar(
        'تم التفريغ',
        'تم تفريغ السلة بنجاح',
        snackPosition: SnackPosition.TOP,
      );
    } catch (e) {
      print('خطأ في تفريغ السلة: $e');
    }
  }

  // التحقق من وجود منتج في السلة
  bool hasProduct(int productId) {
    return _cart.value?.hasProduct(productId) ?? false;
  }

  // الحصول على كمية منتج في السلة
  int getProductQuantity(int productId) {
    final item = _cart.value?.getItem(productId);
    return item?.quantity ?? 0;
  }

  // حساب إجمالي السعر مع الضرائب والشحن
  double getTotalWithTaxAndShipping({
    double taxRate = 0.15,
    double shippingCost = 10.0,
    double freeShippingThreshold = 100.0,
  }) {
    final subtotal = _cart.value?.totalPrice ?? 0.0;
    final tax = subtotal * taxRate;
    final shipping = subtotal >= freeShippingThreshold ? 0.0 : shippingCost;
    
    return subtotal + tax + shipping;
  }

  // تسجيل خروج المستخدم - تفريغ السلة
  Future<void> onUserLogout() async {
    _cart.value = null;
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('user_cart');
  }

  // تسجيل دخول المستخدم - تحميل السلة
  Future<void> onUserLogin(String userId) async {
    await _loadCartFromStorage();
    
    // إذا لم توجد سلة أو كانت لمستخدم مختلف، إنشاء سلة جديدة
    if (_cart.value == null || _cart.value!.userId != userId) {
      await createCart(userId);
    }
  }
}
