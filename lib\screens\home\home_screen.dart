import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../models/product.dart';
import '../../models/user.dart';
import '../../widgets/product_card.dart';
import '../../controllers/auth_controller.dart';
import '../../services/auth_service.dart';

class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final products = Product.demoProducts;

    return Scaffold(
      appBar: AppBar(
        title: const Text("متجر النظارات العصري"),
        backgroundColor: Colors.teal,
        foregroundColor: Colors.white,
        actions: [
          // معلومات المستخدم الحالي
          Obx(() {
            final authService = Get.find<AuthService>();
            final user = authService.currentUser;

            if (user != null) {
              return PopupMenuButton<String>(
                icon: CircleAvatar(
                  backgroundColor: Colors.white,
                  child: Text(
                    user.firstName[0].toUpperCase(),
                    style: const TextStyle(
                      color: Colors.teal,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                onSelected: (value) {
                  if (value == 'logout') {
                    final authController = Get.put(AuthController());
                    authController.logout();
                  }
                },
                itemBuilder: (context) => [
                  PopupMenuItem(
                    enabled: false,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          user.fullName,
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                        Text(
                          user.email,
                          style: const TextStyle(fontSize: 12, color: Colors.grey),
                        ),
                        Text(
                          _getUserRoleText(user.role),
                          style: const TextStyle(fontSize: 12, color: Colors.teal),
                        ),
                      ],
                    ),
                  ),
                  const PopupMenuDivider(),
                  const PopupMenuItem(
                    value: 'logout',
                    child: Row(
                      children: [
                        Icon(Icons.logout, color: Colors.red),
                        SizedBox(width: 8),
                        Text('تسجيل الخروج'),
                      ],
                    ),
                  ),
                ],
              );
            }

            return IconButton(
              icon: const Icon(Icons.login),
              onPressed: () => Get.toNamed('/login'),
            );
          }),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              "منتجاتنا المميزة",
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            SizedBox(
              height: 240,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: products.length,
                itemBuilder: (context, index) {
                  return ProductCard(product: products[index]);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  // وظيفة مساعدة لعرض نوع المستخدم
  String _getUserRoleText(UserRole role) {
    switch (role) {
      case UserRole.customer:
        return 'عميل';
      case UserRole.affiliate:
        return 'مسوق';
      case UserRole.admin:
        return 'مدير';
    }
  }
}
