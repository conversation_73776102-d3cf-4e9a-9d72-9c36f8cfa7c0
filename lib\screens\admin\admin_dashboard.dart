import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../models/user.dart';
import '../../models/product.dart';
import '../../services/auth_service.dart';
import '../../widgets/user_role_widgets.dart';

class AdminDashboard extends StatelessWidget {
  const AdminDashboard({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const UserRoleAppBar(title: "لوحة تحكم الإدارة"),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // إحصائيات سريعة
            _buildStatsCards(),
            
            const SizedBox(height: 24),
            
            // إدارة المستخدمين
            _buildUsersSection(),
            
            const SizedBox(height: 24),
            
            // إدارة المنتجات
            _buildProductsSection(),
            
            const SizedBox(height: 24),
            
            // إعدادات النظام
            _buildSystemSettings(),
          ],
        ),
      ),
    );
  }

  Widget _buildStatsCards() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'إحصائيات عامة',
          style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                'إجمالي المستخدمين',
                '156',
                Icons.people,
                Colors.blue,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildStatCard(
                'إجمالي المنتجات',
                '${Product.demoProducts.length}',
                Icons.inventory,
                Colors.green,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                'إجمالي المبيعات',
                '\$12,450',
                Icons.monetization_on,
                Colors.orange,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildStatCard(
                'الطلبات اليوم',
                '23',
                Icons.shopping_bag,
                Colors.purple,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 24),
                const Spacer(),
                Text(
                  value,
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUsersSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'إدارة المستخدمين',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                ElevatedButton.icon(
                  onPressed: () => _showAddUserDialog(),
                  icon: const Icon(Icons.add),
                  label: const Text('إضافة مستخدم'),
                  style: ElevatedButton.styleFrom(backgroundColor: Colors.blue),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildUsersList(),
          ],
        ),
      ),
    );
  }

  Widget _buildUsersList() {
    // هنا يمكن عرض قائمة المستخدمين الفعلية
    return Column(
      children: [
        _buildUserTile('مدير النظام', '<EMAIL>', 'أدمن', Colors.red),
        _buildUserTile('أحمد المسوق', '<EMAIL>', 'مسوق', Colors.orange),
        _buildUserTile('محمد العميل', '<EMAIL>', 'عميل', Colors.green),
      ],
    );
  }

  Widget _buildUserTile(String name, String email, String role, Color roleColor) {
    return ListTile(
      leading: CircleAvatar(
        backgroundColor: roleColor,
        child: Text(name[0], style: const TextStyle(color: Colors.white)),
      ),
      title: Text(name),
      subtitle: Text(email),
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Chip(
            label: Text(role),
            backgroundColor: roleColor.withOpacity(0.1),
            labelStyle: TextStyle(color: roleColor),
          ),
          PopupMenuButton<String>(
            onSelected: (value) => _handleUserAction(value, email),
            itemBuilder: (context) => [
              const PopupMenuItem(value: 'edit', child: Text('تعديل')),
              const PopupMenuItem(value: 'suspend', child: Text('إيقاف')),
              const PopupMenuItem(value: 'delete', child: Text('حذف')),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildProductsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'إدارة المنتجات',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                ElevatedButton.icon(
                  onPressed: () => _showAddProductDialog(),
                  icon: const Icon(Icons.add),
                  label: const Text('إضافة منتج'),
                  style: ElevatedButton.styleFrom(backgroundColor: Colors.green),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildProductsList(),
          ],
        ),
      ),
    );
  }

  Widget _buildProductsList() {
    final products = Product.demoProducts.take(3).toList();
    
    return Column(
      children: products.map((product) => 
        ListTile(
          leading: ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Image.network(
              product.mainImageUrl,
              width: 50,
              height: 50,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  width: 50,
                  height: 50,
                  color: Colors.grey[300],
                  child: const Icon(Icons.image_not_supported),
                );
              },
            ),
          ),
          title: Text(product.name),
          subtitle: Text('\$${product.price.toStringAsFixed(2)}'),
          trailing: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Chip(
                label: Text('${product.stockQuantity}'),
                backgroundColor: product.stockQuantity > 0 
                    ? Colors.green.withOpacity(0.1)
                    : Colors.red.withOpacity(0.1),
                labelStyle: TextStyle(
                  color: product.stockQuantity > 0 ? Colors.green : Colors.red,
                ),
              ),
              PopupMenuButton<String>(
                onSelected: (value) => _handleProductAction(value, product),
                itemBuilder: (context) => [
                  const PopupMenuItem(value: 'edit', child: Text('تعديل')),
                  const PopupMenuItem(value: 'stock', child: Text('إدارة المخزون')),
                  const PopupMenuItem(value: 'delete', child: Text('حذف')),
                ],
              ),
            ],
          ),
        ),
      ).toList(),
    );
  }

  Widget _buildSystemSettings() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'إعدادات النظام',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(Icons.settings, color: Colors.blue),
              title: const Text('إعدادات عامة'),
              subtitle: const Text('إدارة إعدادات التطبيق'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () => _showSettingsDialog(),
            ),
            ListTile(
              leading: const Icon(Icons.analytics, color: Colors.green),
              title: const Text('التقارير والإحصائيات'),
              subtitle: const Text('عرض تقارير مفصلة'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () => _showReportsDialog(),
            ),
            ListTile(
              leading: const Icon(Icons.backup, color: Colors.orange),
              title: const Text('النسخ الاحتياطي'),
              subtitle: const Text('إدارة النسخ الاحتياطية'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () => _showBackupDialog(),
            ),
          ],
        ),
      ),
    );
  }

  void _showAddUserDialog() {
    Get.snackbar('إضافة مستخدم', 'سيتم فتح نموذج إضافة مستخدم جديد');
  }

  void _showAddProductDialog() {
    Get.snackbar('إضافة منتج', 'سيتم فتح نموذج إضافة منتج جديد');
  }

  void _handleUserAction(String action, String email) {
    Get.snackbar('إجراء المستخدم', 'تم تنفيذ $action للمستخدم $email');
  }

  void _handleProductAction(String action, Product product) {
    Get.snackbar('إجراء المنتج', 'تم تنفيذ $action للمنتج ${product.name}');
  }

  void _showSettingsDialog() {
    Get.snackbar('الإعدادات', 'سيتم فتح صفحة الإعدادات');
  }

  void _showReportsDialog() {
    Get.snackbar('التقارير', 'سيتم فتح صفحة التقارير');
  }

  void _showBackupDialog() {
    Get.snackbar('النسخ الاحتياطي', 'سيتم فتح إعدادات النسخ الاحتياطي');
  }
}
