import 'dart:convert';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user.dart';
import 'cart_service.dart';

class AuthService extends GetxService {
  static AuthService get instance => Get.find();

  // Observable للمستخدم الحالي
  final Rx<User?> _currentUser = Rx<User?>(null);
  User? get currentUser => _currentUser.value;

  // حالة تسجيل الدخول
  final RxBool _isLoggedIn = false.obs;
  bool get isLoggedIn => _isLoggedIn.value;

  // حالة التحميل
  final RxBool _isLoading = false.obs;
  bool get isLoading => _isLoading.value;

  @override
  void onInit() {
    super.onInit();
    _loadUserFromStorage();
  }

  // تحميل بيانات المستخدم من التخزين المحلي
  Future<void> _loadUserFromStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userJson = prefs.getString('current_user');

      if (userJson != null) {
        final userData = json.decode(userJson);
        _currentUser.value = User.fromJson(userData);
        _isLoggedIn.value = true;
      }
    } catch (e) {
      print('Error loading user from storage: $e');
    }
  }

  // حفظ بيانات المستخدم في التخزين المحلي
  Future<void> _saveUserToStorage(User user) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userJson = json.encode(user.toJson());
      await prefs.setString('current_user', userJson);
    } catch (e) {
      print('Error saving user to storage: $e');
    }
  }

  // تسجيل الدخول
  Future<bool> login(String email, String password) async {
    try {
      _isLoading.value = true;

      // محاكاة استدعاء API
      await Future.delayed(const Duration(seconds: 2));

      // البحث عن المستخدم في البيانات التجريبية
      final user = _findUserByEmailAndPassword(email, password);

      if (user != null) {
        _currentUser.value = user.copyWith(
          lastLoginAt: DateTime.now(),
        );
        _isLoggedIn.value = true;

        await _saveUserToStorage(_currentUser.value!);

        // تهيئة سلة التسوق للمستخدم
        await _initializeUserCart();

        Get.snackbar(
          'نجح تسجيل الدخول',
          'مرحباً ${user.fullName}',
          snackPosition: SnackPosition.TOP,
        );

        return true;
      } else {
        Get.snackbar(
          'خطأ في تسجيل الدخول',
          'البريد الإلكتروني أو كلمة المرور غير صحيحة',
          snackPosition: SnackPosition.TOP,
        );
        return false;
      }
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تسجيل الدخول: $e',
        snackPosition: SnackPosition.TOP,
      );
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  // إنشاء حساب جديد
  Future<bool> register({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
    required String phoneNumber,
    required UserRole role,
    String? referralCode,
  }) async {
    try {
      _isLoading.value = true;

      // محاكاة استدعاء API
      await Future.delayed(const Duration(seconds: 2));

      // التحقق من عدم وجود المستخدم مسبقاً
      if (_findUserByEmail(email) != null) {
        Get.snackbar(
          'خطأ في التسجيل',
          'البريد الإلكتروني مستخدم مسبقاً',
          snackPosition: SnackPosition.TOP,
        );
        return false;
      }

      // إنشاء مستخدم جديد
      final newUser = User(
        id: 'user_${DateTime.now().millisecondsSinceEpoch}',
        email: email,
        password: password, // في التطبيق الحقيقي، يجب تشفير كلمة المرور
        firstName: firstName,
        lastName: lastName,
        phoneNumber: phoneNumber,
        role: role,
        createdAt: DateTime.now(),
        affiliateCode: role == UserRole.affiliate
            ? _generateAffiliateCode(firstName, lastName)
            : null,
        referredBy: referralCode,
      );

      // إضافة المستخدم للبيانات التجريبية
      _demoUsers.add(newUser);

      _currentUser.value = newUser;
      _isLoggedIn.value = true;

      await _saveUserToStorage(newUser);

      Get.snackbar(
        'تم إنشاء الحساب بنجاح',
        'مرحباً ${newUser.fullName}',
        snackPosition: SnackPosition.TOP,
      );

      return true;
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء إنشاء الحساب: $e',
        snackPosition: SnackPosition.TOP,
      );
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  // تسجيل الخروج
  Future<void> logout() async {
    try {
      _isLoading.value = true;

      // محاكاة استدعاء API
      await Future.delayed(const Duration(seconds: 1));

      // تنظيف سلة التسوق
      await _clearUserCart();

      _currentUser.value = null;
      _isLoggedIn.value = false;

      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('current_user');

      Get.snackbar(
        'تم تسجيل الخروج',
        'تم تسجيل خروجك بنجاح',
        snackPosition: SnackPosition.TOP,
      );

    } catch (e) {
      print('Error during logout: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  // إعادة تعيين كلمة المرور
  Future<bool> resetPassword(String email) async {
    try {
      _isLoading.value = true;

      // محاكاة استدعاء API
      await Future.delayed(const Duration(seconds: 2));

      final user = _findUserByEmail(email);
      if (user != null) {
        Get.snackbar(
          'تم إرسال رابط إعادة التعيين',
          'تحقق من بريدك الإلكتروني',
          snackPosition: SnackPosition.TOP,
        );
        return true;
      } else {
        Get.snackbar(
          'خطأ',
          'البريد الإلكتروني غير مسجل',
          snackPosition: SnackPosition.TOP,
        );
        return false;
      }
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء إعادة تعيين كلمة المرور: $e',
        snackPosition: SnackPosition.TOP,
      );
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  // البحث عن مستخدم بالبريد الإلكتروني وكلمة المرور
  User? _findUserByEmailAndPassword(String email, String password) {
    try {
      return _demoUsers.firstWhere(
        (user) => user.email.toLowerCase() == email.toLowerCase() &&
                  user.password == password,
      );
    } catch (e) {
      return null;
    }
  }

  // البحث عن مستخدم بالبريد الإلكتروني
  User? _findUserByEmail(String email) {
    try {
      return _demoUsers.firstWhere(
        (user) => user.email.toLowerCase() == email.toLowerCase(),
      );
    } catch (e) {
      return null;
    }
  }

  // إنشاء كود مسوق فريد
  String _generateAffiliateCode(String firstName, String lastName) {
    final timestamp = DateTime.now().millisecondsSinceEpoch.toString().substring(8);
    final initials = '${firstName[0]}${lastName[0]}'.toUpperCase();
    return '$initials$timestamp';
  }

  // تهيئة سلة التسوق للمستخدم
  Future<void> _initializeUserCart() async {
    try {
      if (_currentUser.value != null) {
        final cartService = Get.find<CartService>();
        await cartService.onUserLogin(_currentUser.value!.id);
      }
    } catch (e) {
      print('Error initializing user cart: $e');
    }
  }

  // تنظيف سلة التسوق
  Future<void> _clearUserCart() async {
    try {
      final cartService = Get.find<CartService>();
      await cartService.onUserLogout();
    } catch (e) {
      print('Error clearing user cart: $e');
    }
  }

  // تحويل العميل إلى مسوق
  Future<bool> convertToAffiliate(String userId) async {
    try {
      _isLoading.value = true;

      // محاكاة استدعاء API
      await Future.delayed(const Duration(seconds: 1));

      // البحث عن المستخدم
      final userIndex = _demoUsers.indexWhere((user) => user.id == userId);
      if (userIndex == -1) return false;

      final user = _demoUsers[userIndex];
      if (user.role != UserRole.customer) return false;

      // تحديث المستخدم ليصبح مسوق
      final updatedUser = user.copyWith(
        role: UserRole.affiliate,
        affiliateCode: _generateAffiliateCode(user.firstName, user.lastName),
      );

      _demoUsers[userIndex] = updatedUser;
      _currentUser.value = updatedUser;

      await _saveUserToStorage(updatedUser);

      return true;
    } catch (e) {
      print('Error converting to affiliate: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  // التحقق من صحة البريد الإلكتروني
  bool isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  // التحقق من قوة كلمة المرور
  bool isValidPassword(String password) {
    return password.length >= 6;
  }
}

// بيانات تجريبية للمستخدمين
final List<User> _demoUsers = [
  User(
    id: 'admin_001',
    email: '<EMAIL>',
    password: '123456',
    firstName: 'مدير',
    lastName: 'النظام',
    phoneNumber: '+966501234567',
    role: UserRole.admin,
    createdAt: DateTime.now().subtract(const Duration(days: 100)),
  ),
  User(
    id: 'aff_001',
    email: '<EMAIL>',
    password: '123456',
    firstName: 'أحمد',
    lastName: 'المسوق',
    phoneNumber: '+966507654321',
    role: UserRole.affiliate,
    affiliateCode: 'AM001',
    totalEarnings: 1250.50,
    pendingEarnings: 350.25,
    totalReferrals: 15,
    createdAt: DateTime.now().subtract(const Duration(days: 60)),
  ),
  User(
    id: 'cust_001',
    email: '<EMAIL>',
    password: '123456',
    firstName: 'محمد',
    lastName: 'العميل',
    phoneNumber: '+966509876543',
    role: UserRole.customer,
    totalSpent: 450.75,
    totalOrders: 3,
    createdAt: DateTime.now().subtract(const Duration(days: 30)),
  ),
  User(
    id: 'cust_001',
    email: '<EMAIL>',
    password: '123456',
    firstName: 'فاطمة',
    lastName: 'العميل',
    phoneNumber: '+966509876543',
    role: UserRole.customer,
    referredBy: 'AM001',
    totalSpent: 599.99,
    totalOrders: 3,
    createdAt: DateTime.now().subtract(const Duration(days: 30)),
  ),
];
