enum EyewearType { sunglasses, prescription, reading, sports, fashion }
enum FrameMaterial { metal, plastic, titanium, acetate, wood }
enum LensType { regular, polarized, photochromic, blueLight, progressive }
enum Gender { men, women, unisex, kids }

class EyewearSpecs {
  final FrameMaterial frameMaterial;
  final LensType lensType;
  final String frameColor;
  final String lensColor;
  final String frameSize; // مثل: "52-18-140"
  final double lensWidth; // عرض العدسة بالمم
  final double bridgeWidth; // عرض الجسر بالمم
  final double templeLength; // طول الذراع بالمم
  final bool hasUVProtection;
  final String? prescription; // للنظارات الطبية

  EyewearSpecs({
    required this.frameMaterial,
    required this.lensType,
    required this.frameColor,
    required this.lensColor,
    required this.frameSize,
    required this.lensWidth,
    required this.bridgeWidth,
    required this.templeLength,
    this.hasUVProtection = false,
    this.prescription,
  });

  Map<String, dynamic> toJson() {
    return {
      'frameMaterial': frameMaterial.toString(),
      'lensType': lensType.toString(),
      'frameColor': frameColor,
      'lensColor': lensColor,
      'frameSize': frameSize,
      'lensWidth': lensWidth,
      'bridgeWidth': bridgeWidth,
      'templeLength': templeLength,
      'hasUVProtection': hasUVProtection,
      'prescription': prescription,
    };
  }

  factory EyewearSpecs.fromJson(Map<String, dynamic> json) {
    return EyewearSpecs(
      frameMaterial: FrameMaterial.values.firstWhere(
        (e) => e.toString() == json['frameMaterial'],
        orElse: () => FrameMaterial.plastic,
      ),
      lensType: LensType.values.firstWhere(
        (e) => e.toString() == json['lensType'],
        orElse: () => LensType.regular,
      ),
      frameColor: json['frameColor'] ?? '',
      lensColor: json['lensColor'] ?? '',
      frameSize: json['frameSize'] ?? '',
      lensWidth: (json['lensWidth'] ?? 0.0).toDouble(),
      bridgeWidth: (json['bridgeWidth'] ?? 0.0).toDouble(),
      templeLength: (json['templeLength'] ?? 0.0).toDouble(),
      hasUVProtection: json['hasUVProtection'] ?? false,
      prescription: json['prescription'],
    );
  }
}

class Product {
  final int id;
  final String name;
  final String description;
  final double price;
  final double? originalPrice; // السعر الأصلي قبل التخفيض
  final List<String> imageUrls; // عدة صور للمنتج
  final bool isPopular;
  final bool isRecommended;
  final EyewearType type;
  final Gender targetGender;
  final String brand;
  final EyewearSpecs specs;
  final List<String> features; // ميزات المنتج
  final int stockQuantity;
  final double rating; // تقييم المنتج من 5
  final int reviewCount; // عدد التقييمات
  final String category;
  final List<String> tags; // علامات للبحث
  final DateTime createdAt;
  final bool isActive;

  Product({
    required this.id,
    required this.name,
    required this.description,
    required this.price,
    this.originalPrice,
    required this.imageUrls,
    this.isPopular = false,
    this.isRecommended = false,
    required this.type,
    required this.targetGender,
    required this.brand,
    required this.specs,
    this.features = const [],
    this.stockQuantity = 0,
    this.rating = 0.0,
    this.reviewCount = 0,
    required this.category,
    this.tags = const [],
    required this.createdAt,
    this.isActive = true,
  });

  // Static method to get demo products
  static List<Product> get demoProducts => _demoProducts;

  // Get popular products only
  static List<Product> get popularProducts =>
      _demoProducts.where((product) => product.isPopular).toList();

  // Get recommended products only
  static List<Product> get recommendedProducts =>
      _demoProducts.where((product) => product.isRecommended).toList();

  // Format price as string
  String get formattedPrice => "\$${price.toStringAsFixed(2)}";

  // Getters
  String get mainImageUrl => imageUrls.isNotEmpty ? imageUrls.first : '';
  bool get isOnSale => originalPrice != null && originalPrice! > price;
  double get discountPercentage => isOnSale ? ((originalPrice! - price) / originalPrice!) * 100 : 0;
  bool get isInStock => stockQuantity > 0;
  String get formattedRating => rating.toStringAsFixed(1);

  // Convert to JSON (for API calls)
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'price': price,
      'originalPrice': originalPrice,
      'imageUrls': imageUrls,
      'isPopular': isPopular,
      'isRecommended': isRecommended,
      'type': type.toString(),
      'targetGender': targetGender.toString(),
      'brand': brand,
      'specs': specs.toJson(),
      'features': features,
      'stockQuantity': stockQuantity,
      'rating': rating,
      'reviewCount': reviewCount,
      'category': category,
      'tags': tags,
      'createdAt': createdAt.toIso8601String(),
      'isActive': isActive,
    };
  }

  // Create from JSON (for API responses)
  factory Product.fromJson(Map<String, dynamic> json) {
    return Product(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      price: (json['price'] ?? 0.0).toDouble(),
      originalPrice: json['originalPrice'] != null ? (json['originalPrice']).toDouble() : null,
      imageUrls: List<String>.from(json['imageUrls'] ?? []),
      isPopular: json['isPopular'] ?? false,
      isRecommended: json['isRecommended'] ?? false,
      type: EyewearType.values.firstWhere(
        (e) => e.toString() == json['type'],
        orElse: () => EyewearType.fashion,
      ),
      targetGender: Gender.values.firstWhere(
        (e) => e.toString() == json['targetGender'],
        orElse: () => Gender.unisex,
      ),
      brand: json['brand'] ?? '',
      specs: EyewearSpecs.fromJson(json['specs'] ?? {}),
      features: List<String>.from(json['features'] ?? []),
      stockQuantity: json['stockQuantity'] ?? 0,
      rating: (json['rating'] ?? 0.0).toDouble(),
      reviewCount: json['reviewCount'] ?? 0,
      category: json['category'] ?? '',
      tags: List<String>.from(json['tags'] ?? []),
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
      isActive: json['isActive'] ?? true,
    );
  }
}

// قائمة منتجات وهمية لاختبار العرض - نظارات وإكسسوارات
final List<Product> _demoProducts = [
  Product(
    id: 1,
    name: "نظارة شمسية كلاسيكية",
    description: "نظارة شمسية أنيقة بإطار معدني وعدسات مقاومة للأشعة فوق البنفسجية مع حماية 100% من الأشعة الضارة",
    price: 299.99,
    originalPrice: 399.99,
    imageUrls: [
      "https://images.unsplash.com/photo-1572635196237-14b3f281503f?w=300&h=300&fit=crop",
      "https://images.unsplash.com/photo-1511499767150-a48a237f0083?w=300&h=300&fit=crop",
    ],
    isPopular: true,
    isRecommended: true,
    type: EyewearType.sunglasses,
    targetGender: Gender.unisex,
    brand: "Ray-Ban",
    specs: EyewearSpecs(
      frameMaterial: FrameMaterial.metal,
      lensType: LensType.polarized,
      frameColor: "ذهبي",
      lensColor: "بني متدرج",
      frameSize: "58-15-145",
      lensWidth: 58,
      bridgeWidth: 15,
      templeLength: 145,
      hasUVProtection: true,
    ),
    features: ["حماية UV400", "عدسات مستقطبة", "إطار معدني قوي", "مقاوم للخدش"],
    stockQuantity: 25,
    rating: 4.8,
    reviewCount: 156,
    category: "نظارات شمسية",
    tags: ["شمسية", "كلاسيكية", "معدني", "مستقطبة"],
    createdAt: DateTime.now().subtract(const Duration(days: 30)),
  ),
  Product(
    id: 2,
    name: "نظارة طبية عصرية",
    description: "إطار نظارة طبية مريح ومناسب للاستخدام اليومي مع تصميم عصري وخامات عالية الجودة",
    price: 199.99,
    imageUrls: [
      "https://images.unsplash.com/photo-1574258495973-f010dfbb5371?w=300&h=300&fit=crop",
    ],
    isPopular: true,
    isRecommended: false,
    type: EyewearType.prescription,
    targetGender: Gender.unisex,
    brand: "Oakley",
    specs: EyewearSpecs(
      frameMaterial: FrameMaterial.acetate,
      lensType: LensType.regular,
      frameColor: "أسود مطفي",
      lensColor: "شفاف",
      frameSize: "52-18-140",
      lensWidth: 52,
      bridgeWidth: 18,
      templeLength: 140,
      hasUVProtection: false,
    ),
    features: ["خفيف الوزن", "مريح للاستخدام الطويل", "تصميم عصري", "قابل للتعديل"],
    stockQuantity: 40,
    rating: 4.5,
    reviewCount: 89,
    category: "نظارات طبية",
    tags: ["طبية", "عصرية", "مريحة", "يومية"],
    createdAt: DateTime.now().subtract(const Duration(days: 20)),
  ),
  Product(
    id: 3,
    name: "نظارة رياضية",
    description: "نظارة مصممة خصيصاً للأنشطة الرياضية مع حماية عالية ومقاومة للصدمات والعرق",
    price: 159.99,
    imageUrls: [
      "https://images.unsplash.com/photo-1511499767150-a48a237f0083?w=300&h=300&fit=crop",
    ],
    isPopular: false,
    isRecommended: true,
    type: EyewearType.sports,
    targetGender: Gender.unisex,
    brand: "Nike",
    specs: EyewearSpecs(
      frameMaterial: FrameMaterial.plastic,
      lensType: LensType.polarized,
      frameColor: "أزرق رياضي",
      lensColor: "رمادي",
      frameSize: "60-16-130",
      lensWidth: 60,
      bridgeWidth: 16,
      templeLength: 130,
      hasUVProtection: true,
    ),
    features: ["مقاوم للعرق", "مقاوم للصدمات", "خفيف جداً", "تهوية ممتازة"],
    stockQuantity: 15,
    rating: 4.7,
    reviewCount: 67,
    category: "نظارات رياضية",
    tags: ["رياضية", "مقاومة", "خفيفة", "تهوية"],
    createdAt: DateTime.now().subtract(const Duration(days: 15)),
  ),
  Product(
    id: 4,
    name: "نظارة قراءة أنيقة",
    description: "نظارة قراءة مريحة بتصميم كلاسيكي وجودة عالية مثالية للقراءة والعمل المكتبي",
    price: 89.99,
    imageUrls: [
      "https://images.unsplash.com/photo-1473496169904-658ba7c44d8a?w=300&h=300&fit=crop",
    ],
    isPopular: true,
    isRecommended: true,
    type: EyewearType.reading,
    targetGender: Gender.unisex,
    brand: "Foster Grant",
    specs: EyewearSpecs(
      frameMaterial: FrameMaterial.acetate,
      lensType: LensType.blueLight,
      frameColor: "بني كلاسيكي",
      lensColor: "شفاف مع فلتر أزرق",
      frameSize: "50-20-145",
      lensWidth: 50,
      bridgeWidth: 20,
      templeLength: 145,
      hasUVProtection: false,
    ),
    features: ["فلتر الضوء الأزرق", "مريحة للقراءة", "تصميم كلاسيكي", "خامات ممتازة"],
    stockQuantity: 60,
    rating: 4.3,
    reviewCount: 234,
    category: "نظارات قراءة",
    tags: ["قراءة", "كلاسيكية", "مريحة", "فلتر أزرق"],
    createdAt: DateTime.now().subtract(const Duration(days: 10)),
  ),
];
